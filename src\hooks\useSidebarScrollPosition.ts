'use client';

import { useRef, useEffect } from 'react';

interface UseSidebarScrollPositionOptions {
  /** Unique key for storing scroll position in sessionStorage */
  storageKey: string;
  /** Current pathname for detecting navigation changes */
  pathname: string;
  /** Whether sidebar is collapsed */
  isCollapsed?: boolean;
  /** Additional dependencies that should trigger scroll restoration */
  dependencies?: any[];
}

interface UseSidebarScrollPositionReturn {
  /** Ref to attach to the scrollable container */
  scrollContainerRef: React.RefObject<HTMLDivElement>;
  /** Ref to attach to the currently active navigation item */
  activeItemRef: React.RefObject<HTMLAnchorElement | HTMLButtonElement>;
  /** Function to save current scroll position */
  saveScrollPosition: () => void;
  /** Function to restore saved scroll position */
  restoreScrollPosition: () => void;
  /** Function to scroll to active item if it's out of view */
  scrollToActiveItem: () => void;
  /** Function to handle navigation with scroll position preservation */
  handleNavigation: (callback?: () => void) => void;
}

/**
 * Custom hook for preserving sidebar scroll position across navigation
 * 
 * This hook provides professional UX by maintaining scroll position when users
 * navigate between pages, similar to platforms like YouTube, GitHub, etc.
 * 
 * Features:
 * - Preserves scroll position in sessionStorage
 * - Automatically restores position on navigation
 * - Smooth scrolls to active item if out of view
 * - Handles collapsed/expanded sidebar states
 * - Cleans up on unmount
 * 
 * @param options Configuration options
 * @returns Object with refs and utility functions
 */
export function useSidebarScrollPosition({
  storageKey,
  pathname,
  isCollapsed = false,
  dependencies = []
}: UseSidebarScrollPositionOptions): UseSidebarScrollPositionReturn {
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const activeItemRef = useRef<HTMLAnchorElement | HTMLButtonElement>(null);

  // Save scroll position to sessionStorage
  const saveScrollPosition = () => {
    if (scrollContainerRef.current) {
      const scrollTop = scrollContainerRef.current.scrollTop;
      sessionStorage.setItem(storageKey, scrollTop.toString());
    }
  };

  // Restore scroll position from sessionStorage
  const restoreScrollPosition = () => {
    if (scrollContainerRef.current) {
      const savedScrollTop = sessionStorage.getItem(storageKey);
      if (savedScrollTop) {
        scrollContainerRef.current.scrollTop = parseInt(savedScrollTop, 10);
      }
    }
  };

  // Smooth scroll to active item if it's out of view
  const scrollToActiveItem = () => {
    if (activeItemRef.current && scrollContainerRef.current) {
      const container = scrollContainerRef.current;
      const activeItem = activeItemRef.current;
      
      const containerRect = container.getBoundingClientRect();
      const itemRect = activeItem.getBoundingClientRect();
      
      // Check if item is out of view
      const isOutOfView = itemRect.top < containerRect.top || itemRect.bottom > containerRect.bottom;
      
      if (isOutOfView) {
        activeItem.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        });
      }
    }
  };

  // Handle navigation with scroll position preservation
  const handleNavigation = (callback?: () => void) => {
    saveScrollPosition();
    if (callback) {
      callback();
    }
  };

  // Restore scroll position on mount and when dependencies change
  useEffect(() => {
    const timer = setTimeout(() => {
      restoreScrollPosition();
      scrollToActiveItem();
    }, 100); // Small delay to ensure DOM is ready

    return () => clearTimeout(timer);
  }, [pathname, isCollapsed, ...dependencies]);

  // Save scroll position when component unmounts
  useEffect(() => {
    return () => {
      saveScrollPosition();
    };
  }, []);

  return {
    scrollContainerRef,
    activeItemRef,
    saveScrollPosition,
    restoreScrollPosition,
    scrollToActiveItem,
    handleNavigation
  };
}

export default useSidebarScrollPosition;
